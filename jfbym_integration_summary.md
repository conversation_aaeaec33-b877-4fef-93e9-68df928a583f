# jfbym.com OCR Platform Integration Summary

## Overview
Successfully integrated the jfbym.com OCR platform into the existing GUI interface as an additional OCR option alongside the existing ttshitu.com service.

## Changes Made

### 1. Added jfbym.com API Function
- **File**: `ldzk_yj.py`
- **Function**: `jfbym_api_bytes(token, type_id, image_bytes)`
- **Location**: Lines 65-82
- **Purpose**: Handles API calls to jfbym.com OCR service

### 2. Enhanced OCR Settings Tab
- **Added Platform Selection**: Dropdown to choose between "ttshitu.com" and "jfbym.com"
- **Added jfbym.com Fields**:
  - "云码token:" - Text input for user's jfbym.com token
  - "类型ID:" - Text input for OCR type ID
- **Dynamic UI**: Fields show/hide based on selected platform

### 3. Updated OCR Recognition Logic
- **Modified Functions**:
  - `start_ocr_recognition()` - Main batch OCR function
  - `single_ocr_recognition()` - Single emulator OCR function
- **Added New Function**: `process_emulator_ocr_jfbym()` - Handles jfbym.com OCR processing

### 4. Platform Switching Logic
- **Function**: `on_ocr_platform_changed(platform)`
- **Purpose**: Shows/hides relevant input fields based on selected platform

## API Integration Details

### jfbym.com API Specifications
- **Endpoint**: `http://api.jfbym.com/api/YmServer/customApi`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Request Format**:
  ```json
  {
    "token": "user_token",
    "type": "type_id", 
    "image": "base64_encoded_image"
  }
  ```

### Response Handling
- **Success Code**: `code: 10000`
- **Result Location**: `data.data` field
- **Error Message**: `msg` field
- **Coordinate Format**: Same as ttshitu.com (x,y|x,y|...)

## User Interface Changes

### OCR Settings Tab Layout
1. **OCR Platform Dropdown**: Select between ttshitu.com and jfbym.com
2. **ttshitu.com Fields** (shown when ttshitu.com selected):
   - Username input
   - Password input (masked)
3. **jfbym.com Fields** (shown when jfbym.com selected):
   - Token input
   - Type ID input

### Usage Instructions
1. Navigate to "OCR 设置" tab
2. Select desired OCR platform from dropdown
3. Enter appropriate credentials:
   - For ttshitu.com: username and password
   - For jfbym.com: token and type ID
4. Use "开始识别" button or right-click "识别打码" for OCR recognition

## Technical Implementation

### Error Handling
- Validates required fields before API calls
- Handles API response errors gracefully
- Provides user-friendly error messages in log

### Coordinate Processing
- Both platforms use same coordinate adjustment logic
- Maintains compatibility with existing crop parameters
- Supports multiple coordinate points (x,y|x,y format)

### Threading
- Both OCR platforms use same thread pool executor
- Maintains concurrent processing capabilities
- Proper exception handling in threaded environment

## Testing
- Created test script: `test_jfbym_api.py`
- Validates API function structure and error handling
- Can be used to test actual API calls with real credentials

## Backward Compatibility
- Existing ttshitu.com functionality unchanged
- Default platform remains ttshitu.com
- All existing features continue to work as before

## Next Steps
1. Test with actual jfbym.com credentials
2. Verify coordinate accuracy with real captcha images
3. Consider adding more OCR platforms if needed
4. Add configuration persistence for platform selection
