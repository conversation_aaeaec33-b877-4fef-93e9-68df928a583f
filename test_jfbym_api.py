#!/usr/bin/env python3
"""
Test script for jfbym.com OCR API integration
"""

import base64
import requests
from io import BytesIO

def jfbym_api_bytes(token, type_id, image_bytes):
    """jfbym.com OCR API 调用函数"""
    try:
        image_base64 = base64.b64encode(image_bytes).decode()
        url = "http://api.jfbym.com/api/YmServer/customApi"
        data = {
            "token": token,
            "type": type_id,
            "image": image_base64,
        }
        headers = {
            "Content-Type": "application/json"
        }
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error during jfbym API call: {type(e).__name__}: {e}")
        return {"success": False, "errstr": str(e)}

def test_api():
    """测试 API 函数"""
    # 创建一个简单的测试图像字节数据
    test_image_bytes = b"test_image_data"
    
    # 测试参数
    test_token = "your_token_here"
    test_type_id = "your_type_id_here"
    
    print("Testing jfbym.com OCR API function...")
    print(f"Token: {test_token}")
    print(f"Type ID: {test_type_id}")
    
    # 调用 API 函数
    result = jfbym_api_bytes(test_token, test_type_id, test_image_bytes)
    
    print("API Response:")
    print(result)
    
    # 检查响应格式
    if result and result.get("code") == 10000:
        print("✓ API call successful (based on expected response format)")
        data = result.get("data", {})
        if data:
            result_str = data.get("data")
            print(f"OCR Result: {result_str}")
        else:
            print("No data in response")
    else:
        error_message = result.get("msg") if result else "Unknown error"
        print(f"✗ API call failed: {error_message}")

if __name__ == "__main__":
    test_api()
