import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QTableWidget,
                            QTableWidgetItem, QMessageBox, QFileDialog,
                            QLineEdit, QHeaderView, QCheckBox, QTextEdit,
                            QSplitter, QGroupBox, QComboBox, QFormLayout,
                            QTabWidget,QGridLayout, QMenu)  # 导入 QMenu
from PyQt6.QtCore import Qt, QSettings, QThread, pyqtSignal
from PyQt6.QtGui import QIcon, QColor, QBrush, QFont, QAction
from datetime import datetime
from PIL import Image
from io import BytesIO
import subprocess
import time
import base64
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入Dnconsole类 (这部分代码保持不变)
from ldconsole import Dnconsole

# 从 ocr_2.py 导入函数 (这部分代码保持不变)
def get_exe_dir():
    if getattr(sys, "frozen", False):
        return os.path.dirname(sys.executable)
    else:
        return os.path.dirname(os.path.abspath(__file__))

def base64_api(username, password, image_path, typeid=20):
    try:
        with open(image_path, "rb") as f:
            image_data = f.read()
        image_base64 = base64.b64encode(image_data).decode()
        data = {
            "username": username,
            "password": password,
            "typeid": typeid,
            "image": image_base64,
        }
        response = requests.post("http://api.ttshitu.com/predict", data=data)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error during API call: {type(e).__name__}: {e}")  # 打印异常类型
        return {"success": False, "errstr": str(e)}

def base64_api_bytes(username, password, image_bytes, typeid=20):
    try:
        image_base64 = base64.b64encode(image_bytes).decode()
        data = {
            "username": username,
            "password": password,
            "typeid": typeid,
            "image": image_base64,
        }
        response = requests.post("http://api.ttshitu.com/predict", data=data)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"Error during API call: {type(e).__name__}: {e}")
        return {"success": False, "errstr": str(e)}    


class RefreshThread(QThread):
    finished = pyqtSignal(list)  # 完成信号
    progress = pyqtSignal(str)   # 进度信号

    def __init__(self, controller):
        super().__init__()
        self.controller = controller
        self._is_running = True

    def run(self):
        try:
            if self._is_running:
                instances = self.controller.get_all_instances()
                self.finished.emit(instances)
        except Exception as e:
            self.progress.emit(f"刷新失败: {type(e).__name__}: {e}")

    def stop(self):
        """停止线程"""
        self._is_running = False

class Dnconsole_OCR:
    def __init__(self, ldconsole_path, adb_path):
        self.ldconsole_path = ldconsole_path
        self.adb_path = adb_path

    def launch(self, index):
        subprocess.run(
            f'"{self.ldconsole_path}" launch --index {index}', shell=True
        )

    def quit(self, index):
        subprocess.run(f'"{self.ldconsole_path}" quit --index {index}', shell=True)

    def quitall(self):
        subprocess.run(f'"{self.ldconsole_path}" quitall', shell=True)

    def isrunning(self, index):
        result = subprocess.run(
            f'"{self.ldconsole_path}" isrunning --index {index}',
            shell=True,
            capture_output=True,
            text=True,
        )
        return "running" in result.stdout

    def ADB(self, cmd, index, port):
        emulator_device_name = f"127.0.0.1:{port}"
        print(f"Trying ADB command with device: {emulator_device_name}")
        try:
            result = subprocess.run(
                f'"{self.adb_path}" -s {emulator_device_name} {cmd}',
                shell=True,
                capture_output=True,
                text=True,
                timeout=60  # 设置超时时间为60秒
            )
            print(result.stdout)
            if result.returncode != 0:
                print(result.stderr)
                return False, result.stderr
            return True, result.stdout
        except subprocess.TimeoutExpired:  # 捕获超时异常
            print(f"ADB command timed out for device: {emulator_device_name}")
            return False, "ADB command timed out"
        except Exception as e:
            print(f"ADB command failed: {type(e).__name__}: {e}")
            return False, str(e)

class LDController:
    def __init__(self, ld_path="C:\\LDPlayer\\LDPlayer4.0"):
        self.ld_path = ld_path
        self.console_path = os.path.join(ld_path, "ldconsole.exe")
        self.adb_path = os.path.join(ld_path, "adb.exe")  # 添加 adb 路径
        self.Dc = Dnconsole(ld_path)
        self.Dc_ocr = Dnconsole_OCR(self.console_path,self.adb_path)

    def get_all_instances(self):
        """获取所有模拟器实例信息"""
        try:
            result = self.Dc.list()
            if not result:
                return []

            instances = []
            for line in result.strip().split('\n'):
                if line.strip():
                    try:
                        parts = line.strip().split(',')
                        if len(parts) >= 5:  # 确保至少有5个值
                            index = parts[0]
                            name = parts[1]
                            is_running = (parts[4] == '1')  # 直接从第5个值判断运行状态
                            port = 5555 + int(index)*2 if is_running else None
                            instances.append({
                                'index': index,
                                'name': name,
                                'status': '运行中' if is_running else '未运行',
                                'port':port
                            })
                    except Exception as e:
                        print(f"解析模拟器列表出错: {type(e).__name__}: {e}")
                        continue
            return instances
        except Exception as e:
            print(f"获取模拟器列表失败: {type(e).__name__}: {e}")
            return []


    def close_emulator(self, index):
        """关闭模拟器"""
        return self.Dc.quit(index)

    def close_all_emulators(self):
        """关闭所有模拟器"""
        return self.Dc.quitall()

    def input_text(self, index, text):
        """使用ADB输入文本"""
        try:
            device_name = f"127.0.0.1:{5555 + int(index)*2}"
            self.Dc.ADB(f"connect {device_name}")
            self.Dc.ADB(f"-s {device_name} shell input text \"{text}\"")
            return True
        except Exception as e:
            print(f"输入文本时出错: {type(e).__name__}: {e}")
            return False

    def install_apk(self, index, apk_path):
        """安装APK"""
        try:
            cmd = f'"{self.console_path}" installapp --index {index} --filename "{apk_path}"'
            result = os.popen(cmd).read()
            return True
        except Exception as e:
            print(f"安装APK出错: {type(e).__name__}: {e}")
            return False

    def uninstall_apk(self, index, package_name):
        """卸载APK"""
        try:
            cmd = f'"{self.console_path}" uninstallapp --index {index} --packagename {package_name}'
            result = os.popen(cmd).read()
            return True
        except Exception as e:
            print(f"卸载APK出错: {type(e).__name__}: {e}")
            return False

    def get_package_name(self, apk_path):
        """获取APK包名"""
        try:
            aapt_path = os.path.join(self.ld_path, "aapt.exe")
            if not os.path.exists(aapt_path):
                print("错误: 未找到aapt.exe")
                return None

            import subprocess
            cmd = f'"{aapt_path}" dump badging "{apk_path}"'
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
            output, error = process.communicate()

            if output:
                output_str = output.decode('utf-8', errors='ignore')
                for line in output_str.split('\n'):
                    if 'package: name=' in line:
                        package_name = line.split("name='")[1].split("'")[0]
                        return package_name
            return None
        except Exception as e:
            print(f"获取包名出错: {type(e).__name__}: {e}")
            return None
    def take_screenshot(self, index, port):
        """截图"""
        exe_dir = get_exe_dir()
        local_screenshot_path = os.path.join(exe_dir, f"screenshot_{index}.png")
        try:
            self.Dc_ocr.ADB(f"shell screencap -p /sdcard/screenshot.png", index=index, port=port)
            self.Dc_ocr.ADB(f"pull /sdcard/screenshot.png {local_screenshot_path}", index=index, port=port)
            return local_screenshot_path
        except Exception as e:
            print(f"截图出错: {type(e).__name__}: {e}")
            return None

    def tap_screen(self, index, x, y,port):
        """点击屏幕"""
        try:
            self.Dc_ocr.ADB(f"shell input tap {x} {y}", index=index, port=port)
            return True
        except Exception as e:
            print(f"点击屏幕出错: {type(e).__name__}: {e}")
            return False
    def launch_emulator(self, index):
        """启动模拟器"""
        try:
            self.Dc_ocr.launch(index)
            return True
        except Exception as e:
            print(f"启动模拟器出错: {type(e).__name__}: {e}")
            return False

    def quit_emulator(self, index):
        """关闭模拟器"""
        try:
            self.Dc_ocr.quit(index)
            return True
        except Exception as e:
            print(f"关闭模拟器出错: {type(e).__name__}: {e}")
            return False

class MainWindow(QMainWindow):
    # 类级别属性 (裁剪参数)
    CROP_X = 60
    CROP_Y = 350
    CROP_WIDTH = 420
    CROP_HEIGHT = 380
    
    def __init__(self):
        super().__init__()
        self.controller = None
        self.checked_items = {}
        self.settings = QSettings('LDManager', 'Paths')
        self.refresh_thread = None
        self.executor = ThreadPoolExecutor(max_workers=8)  # 限制并发数
        # 设置窗口图标
        from PyQt6.QtGui import QIcon
        self.setWindowIcon(QIcon('ld.ico'))
        import os
        icon_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'ld.ico'))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
            # 设置任务栏图标
            import ctypes
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('leidian.manager')

        self.init_ui()
        self.load_last_path()

    def init_ui(self):
        self.setWindowTitle('雷电模拟器管理工具')
        self.setGeometry(100, 100, 800, 600)  # 稍微调整了初始大小

        # 设置全局样式 (保持不变，代码较长，省略) ...
        self.setStyleSheet("""
           QMainWindow {
                background-color: #f5f6fa;
            }
            QLabel {
                font-size: 12px;
                color: #2c3e50;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 3px;
                font-size: 12px;
                min-width: 60px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #2475a8;
            }
            QLineEdit {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
                min-height: 25px;
            }
            QLineEdit:read-only {
                background-color: #f0f0f0;
            }
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
                padding: 5px;
                font-family: "Microsoft YaHei", Arial;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
                color: #2c3e50;
            }
            QCheckBox {
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                background-color: #3498db;
                border-radius: 3px;
            }
            QSplitter::handle {
                width: 2px;/*调整分割线宽度*/
                background-color: #bdc3c7;
            }
            QTabWidget {
                border: none;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #bdc3c7;
                border-bottom-color: #ffffff; /* Make bottom-border same as tab pane */
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 80px;
                padding: 5px;
            }
            QTabBar::tab:selected, QTabBar::tab:hover {
                background: #ffffff;
            }
            /* 添加 QComboBox 样式 */
            QComboBox {
                padding: 3px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: 0px;
                width: 25px;
                background: #3498db;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMiA0TDYgOEwxMCA0IiBzdHJva2U9IiNGRkZGRkYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+);
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
            }
            QComboBox:hover {
                border: 1px solid #3498db;
            }
        """)

        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)  # 主布局

        # 顶部设置区域 (保持基本设置)
        top_group = QGroupBox("基本设置")
        top_layout = QVBoxLayout(top_group)
        top_layout.setSpacing(5)

        # 模拟器路径, APK路径, 包名 (保持不变) ...
        # 模拟器路径选择
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("模拟器路径:"))

        self.path_combo = QComboBox()
        self.path_combo.setEditable(True)
        self.path_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        self.path_combo.currentTextChanged.connect(self.on_path_changed)
        self.path_combo.setMinimumHeight(30)
        # QComboBox 样式已在全局样式中设置
        path_layout.addWidget(self.path_combo)
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_ld_path)
        path_layout.addWidget(browse_btn)
        top_layout.addLayout(path_layout)

        # APK路径选择
        apk_layout = QHBoxLayout()
        apk_layout.addWidget(QLabel("APK路径:"))
        self.apk_input = QLineEdit()
        self.apk_input.setAcceptDrops(True)
        self.apk_input.dragEnterEvent = self.dragEnterEvent
        self.apk_input.dropEvent = self.dropEvent
        apk_layout.addWidget(self.apk_input)
        top_layout.addLayout(apk_layout)

        # APK包名显示
        package_layout = QHBoxLayout()
        package_layout.addWidget(QLabel("APK包名:"))
        self.package_label = QLineEdit()
        self.package_label.setReadOnly(True)
        package_layout.addWidget(self.package_label)
        top_layout.addLayout(package_layout)

        main_layout.addWidget(top_group)  # 添加顶部设置

        # 中间区域 (模拟器列表和标签页)
        center_layout = QHBoxLayout()  # 水平布局
        main_layout.addLayout(center_layout) # 添加到主布局



        # 使用 QTabWidget 创建标签页
        self.tab_widget = QTabWidget()
        center_layout.addWidget(self.tab_widget)


        # "操作" 标签页
        self.apk_tab = QWidget()
        apk_layout = QVBoxLayout(self.apk_tab)

        # 主要按钮 (使用 QGridLayout, 分成三列)
        main_buttons_layout = QGridLayout()
        install_btn = QPushButton("一键安装")
        install_btn.clicked.connect(self.batch_install_apk)
        install_btn.setStyleSheet("""
            QPushButton { background-color: #2ecc71; }
            QPushButton:hover { background-color: #27ae60; }
        """)
        uninstall_btn = QPushButton("一键卸载")
        uninstall_btn.clicked.connect(self.batch_uninstall_apk)
        uninstall_btn.setStyleSheet("""
            QPushButton { background-color: #e74c3c; }
            QPushButton:hover { background-color: #c0392b; }
        """)
        input_btn = QPushButton("一键输入")
        input_btn.clicked.connect(self.batch_input_text)
        input_btn.setStyleSheet("""
            QPushButton { background-color: #3498db; }
            QPushButton:hover { background-color: #2980b9; }
        """)
        self.start_ocr_button = QPushButton("开始识别")
        self.start_ocr_button.clicked.connect(self.start_ocr_recognition)
        self.start_ocr_button.setStyleSheet("""
            QPushButton { background-color: #1abc9c; }
            QPushButton:hover { background-color: #16a085; }
        """)
        refresh_btn = QPushButton("刷新列表")
        refresh_btn.clicked.connect(self.refresh_emulator_list)
        refresh_btn.setStyleSheet("""
            QPushButton { background-color: #f39c12; }
            QPushButton:hover { background-color: #e67e22; }
        """)
        check_running_btn = QPushButton("勾选已启动")
        check_running_btn.clicked.connect(self.check_running)
        check_running_btn.setStyleSheet("""
            QPushButton { background-color: #9b59b6; }
            QPushButton:hover { background-color: #8e44ad; }
        """)
        close_all_btn = QPushButton("关闭所有")
        close_all_btn.clicked.connect(self.close_all_emulators)
        close_all_btn.setStyleSheet("""
            QPushButton { background-color: #e74c3c; }
            QPushButton:hover { background-color: #c0392b; }
        """)

        # 添加按钮到网格布局 (调整顺序)
        main_buttons_layout.addWidget(install_btn, 0, 0)
        main_buttons_layout.addWidget(uninstall_btn, 0, 1)
        main_buttons_layout.addWidget(refresh_btn, 0, 2)
        main_buttons_layout.addWidget(close_all_btn, 1, 0)
        main_buttons_layout.addWidget(self.start_ocr_button, 1, 1)
        main_buttons_layout.addWidget(check_running_btn, 1, 2)
        main_buttons_layout.addWidget(input_btn, 2, 0, 1, 3)  # 跨越三列

        apk_layout.addLayout(main_buttons_layout)


        # 日志框 (在 apk_tab 中)
        log_label = QLabel("操作日志:")
        apk_layout.addWidget(log_label)
        self.log_text = QTextEdit()
        self.log_text.setPlaceholderText("操作日志...")
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                color: #2c3e50;
                font-family: Consolas, Monaco, monospace;
            }
        """)
        apk_layout.addWidget(self.log_text)
        self.tab_widget.addTab(self.apk_tab, "操作")

        # "文本" 标签页
        self.text_tab = QWidget()
        text_layout = QVBoxLayout(self.text_tab)
        # 文本输入框
        input_label = QLabel("输入文本:")
        text_layout.addWidget(input_label)
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("请输入要发送的文本...\n每行文本对应一个勾选的模拟器")
        text_layout.addWidget(self.text_input)

        # 日志框
        #log_label = QLabel("操作日志:")
        #text_layout.addWidget(log_label)
        #self.log_text = QTextEdit()
        #self.log_text.setPlaceholderText("操作日志...")
        #self.log_text.setReadOnly(True)
        #self.log_text.setStyleSheet("""
        #    QTextEdit {
        #        background-color: #f8f9fa;
        #        color: #2c3e50;
        #        font-family: Consolas, Monaco, monospace;
        #    }
        #""")
        #text_layout.addWidget(self.log_text)
        self.tab_widget.addTab(self.text_tab, "文本")

        # "OCR 设置" 标签页
        self.ocr_tab = QWidget()
        ocr_layout = QFormLayout(self.ocr_tab)
        self.ocr_user_input = QLineEdit()
        self.ocr_pass_input = QLineEdit()
        self.ocr_pass_input.setEchoMode(QLineEdit.EchoMode.Password)
        ocr_layout.addRow("打码平台用户名:", self.ocr_user_input)
        ocr_layout.addRow("打码平台密码:", self.ocr_pass_input)
        
        # 设置默认用户名和密码
        self.ocr_user_input.setText("sijizz")
        self.ocr_pass_input.setText("qwer1234")
        

        self.tab_widget.addTab(self.ocr_tab, "OCR 设置")
        
            # 模拟器列表
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(['选择', '名称', '状态'])
        self.table.setColumnWidth(0, 30)
        self.table.setColumnWidth(1, 100)
        self.table.setColumnWidth(2, 50)
        self.table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 5px;
                border: none;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        self.table.setFixedHeight(400)  # 设置固定高度
        center_layout.addWidget(self.table)
        self.table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)  # 启用自定义上下文菜单
        self.table.customContextMenuRequested.connect(self.show_context_menu)


    def show_context_menu(self, position):
        """显示右键菜单"""
        index_item = self.table.itemAt(position)
        if not index_item:
            return

        row = index_item.row()
        index = self.table.item(row, 1).data(Qt.ItemDataRole.UserRole)  # 获取模拟器索引

        menu = QMenu(self)

        # 启动模拟器
        launch_action = QAction("启动模拟器", self)
        launch_action.triggered.connect(lambda: self.launch_selected_emulator(index))
        menu.addAction(launch_action)
        
        # 关闭模拟器
        quit_action = QAction("关闭模拟器", self)
        quit_action.triggered.connect(lambda: self.quit_selected_emulator(index))
        menu.addAction(quit_action)

        # 添加分隔线
        menu.addSeparator()

        # 识别打码 (与 "开始识别" 按钮功能相同，但只针对单个模拟器)
        ocr_action = QAction("识别打码", self)
        ocr_action.triggered.connect(lambda: self.single_ocr_recognition(index))
        menu.addAction(ocr_action)


        menu.exec(self.table.viewport().mapToGlobal(position))

    def launch_selected_emulator(self, index):
        """启动选中的模拟器"""
        if not self.controller:
            self.add_log("错误: 请先选择雷电模拟器安装路径！")
            return
        if self.controller.launch_emulator(int(index)):
            self.add_log(f"模拟器 {index} 启动成功")
            self.refresh_emulator_list()  # 刷新列表以更新状态
        else:
            self.add_log(f"模拟器 {index} 启动失败")

    def quit_selected_emulator(self, index):
        """关闭选中的模拟器"""
        if not self.controller:
            self.add_log("错误: 请先选择雷电模拟器安装路径！")
            return
        if self.controller.quit_emulator(int(index)):
            self.add_log(f"模拟器 {index} 关闭成功")
            self.refresh_emulator_list()
        else:
            self.add_log(f"模拟器 {index} 关闭失败")

    def single_ocr_recognition(self, index):
        """对单个模拟器进行 OCR 识别"""
        username = self.ocr_user_input.text()
        password = self.ocr_pass_input.text()

        if not username or not password:
            self.add_log("错误: 请输入打码平台用户名和密码！")
            return
        instances = self.controller.get_all_instances()
        selected_port = None
        for instance in instances:
            if instance['index'] == index:
                selected_port= instance['port']
        if selected_port is not None:
            self.process_emulator_ocr(index, username, password,selected_port)
        else:
            self.add_log(f"模拟器 {index} 未运行，无法识别")

    def load_last_path(self):
        """加载历史路径"""
        paths = self.settings.value('paths', [])
        if paths:
            # 检查所有路径是否在当前系统上存在
            valid_paths = [path for path in paths if os.path.exists(path)]

            # 如果有无效路径，清除所有记录
            if len(valid_paths) != len(paths):
                self.settings.clear()  # 清除所有设置
                self.add_log("检测到系统环境改变，已清除历史记录")
                return

            self.path_combo.addItems(paths)
            last_path = self.settings.value('last_path')
            if last_path and os.path.exists(last_path):
                index = self.path_combo.findText(last_path)
                if index >= 0:
                    self.path_combo.setCurrentIndex(index)
                    self.controller = LDController(last_path)
                    self.refresh_emulator_list()
    def save_path(self, path):
        """保存路径到历史记录"""
        paths = [self.path_combo.itemText(i) for i in range(self.path_combo.count())]
        if path not in paths:
            self.path_combo.insertItem(0, path)
            if self.path_combo.count() > 5:  # 最多保存5条记录
                self.path_combo.removeItem(5)
        else:
            index = paths.index(path)
            if index != 0:  # 如果不是第一个，移到第一个
                self.path_combo.removeItem(index)
                self.path_combo.insertItem(0, path)

        self.path_combo.setCurrentIndex(0)

        # 保存到设置
        paths = [self.path_combo.itemText(i) for i in range(self.path_combo.count())]
        self.settings.setValue('paths', paths)
        self.settings.setValue('last_path', path)

    def on_path_changed(self, path):
        """路径改变时的处理"""
        if path and os.path.exists(path):
            ldconsole_path = os.path.join(path, "ldconsole.exe")
            if os.path.exists(ldconsole_path):
                self.controller = LDController(path)
                self.refresh_emulator_list()

    def add_log(self, message):
        """添加日志"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{current_time}] {message}")
        # 滚动到最底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )

    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event):
        urls = event.mimeData().urls()
        if urls and urls[0].isLocalFile():
            apk_path = urls[0].toLocalFile()
            self.apk_input.setText(apk_path)
            # 自动获取并显示包名
            if apk_path.lower().endswith('.apk'):
                if not self.controller:
                    self.add_log("错误: 请先选择雷电模拟器安装路径")
                    return

                package_name = self.controller.get_package_name(apk_path)
                if package_name:
                    self.package_label.setText(package_name)
                    self.add_log(f"获取APK包名: {package_name}")
                else:
                    self.package_label.setText("无法获取包名")
                    self.add_log("无法获取APK包名")

    def browse_ld_path(self):
        default_dir = self.path_combo.currentText()
        if not os.path.exists(default_dir):
            default_dir = ''

        folder = QFileDialog.getExistingDirectory(self, "选择雷电模拟器安装路径", default_dir)
        if folder:
            ldconsole_path = os.path.join(folder, "ldconsole.exe")
            if os.path.exists(ldconsole_path):
                self.save_path(folder)
                self.add_log(f"设置模拟器路径: {folder}")
                self.controller = LDController(folder)
                self.refresh_emulator_list()
            else:
                self.add_log("错误: 所选目录不是有效的雷电模拟器目录！请选择包含 ldconsole.exe 的目录。")

    def refresh_emulator_list(self):
        """刷新模拟器列表"""
        if not self.controller:
            return

        instances = self.controller.get_all_instances()
        if instances is None:
            instances = []
        # 如果有正在运行的线程，先停止它
        if self.refresh_thread and self.refresh_thread.isRunning():
            self.refresh_thread.stop()
            self.refresh_thread.wait()

        # 创建并启动刷新线程
        self.refresh_thread = RefreshThread(self.controller)
        self.refresh_thread.finished.connect(self.on_refresh_complete)
        self.refresh_thread.progress.connect(self.add_log)
        self.refresh_thread.start()

    def on_refresh_complete(self, instances):
        """刷新完成的处理"""
        if instances is None:
            instances = []

        self.table.setRowCount(len(instances))

        for row, instance in enumerate(instances):
            # 复选框
            checkbox = QCheckBox()
            if instance['index'] in self.checked_items:
                checkbox.setChecked(self.checked_items[instance['index']])
            checkbox.stateChanged.connect(lambda state, idx=instance['index']: self.on_checkbox_changed(state, idx))
            self.table.setCellWidget(row, 0, checkbox)

            # 名称
            name_item = QTableWidgetItem(instance['name'])
            self.table.setItem(row, 1, name_item)
            # 将索引存储在 "UserRole" 中，方便右键菜单使用
            self.table.item(row, 1).setData(Qt.ItemDataRole.UserRole, instance['index'])

            # 状态（带高亮）
            status_item = QTableWidgetItem(instance['status'])
            if instance['status'] == '运行中':
                status_item.setBackground(QBrush(QColor(144, 238, 144)))  # 浅绿色
            else:
                status_item.setBackground(QBrush(QColor(255, 228, 225)))  # 浅红色
            self.table.setItem(row, 2, status_item)


        self.add_log("刷新模拟器列表完成")

    def check_running(self):
        """勾选所有运行中的模拟器"""
        count = 0
        for row in range(self.table.rowCount()):
            status_item = self.table.item(row, 2)
            checkbox = self.table.cellWidget(row, 0)
            if status_item and status_item.text() == '运行中':
                checkbox.setChecked(True)
                count += 1
            else:
                checkbox.setChecked(False)
        self.add_log(f"已勾选 {count} 个运行中的模拟器")

    def on_checkbox_changed(self, state, index):
        self.checked_items[index] = (state == Qt.CheckState.Checked.value)

    def close_all_emulators(self):
        if not self.controller:
            self.add_log("错误: 请先选择雷电模拟器安装路径！")
            return

        reply = QMessageBox.question(self, "确认关闭",
                                    "确定要关闭所有模拟器吗？",
                                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.controller.close_all_emulators()
            self.add_log("关闭所有模拟器")
            self.refresh_emulator_list()

    def batch_input_text(self):
        """批量输入文本到选中的模拟器"""
        if not self.controller:
            self.add_log("错误: 请先选择雷电模拟器安装路径！")
            return

        # 获取文本框中的所有行
        text_lines = self.text_input.toPlainText().splitlines()
        if not text_lines:
            self.add_log("错误: 请输入要发送的文本！")
            return

        # 获取选中的模拟器索引
        selected_indices = [idx for idx, checked in self.checked_items.items() if checked]
        selected_indices.sort(key=lambda x: int(x))  # 按数字大小排序

        if not selected_indices:
            self.add_log("错误: 请选择至少一个模拟器！")
            return

        success_count = 0
        remaining_lines = []
        self.add_log("开始批量输入文本...")

        for i, line in enumerate(text_lines):
            if i < len(selected_indices):
                index = selected_indices[i]
                if self.controller.input_text(index, line):
                    success_count += 1
                    self.add_log(f"模拟器 {index} 文本输入成功: {line}")
                else:
                    self.add_log(f"模拟器 {index} 文本输入失败")
                    remaining_lines.append(line)
            else:
                remaining_lines.append(line)

        # 更新文本框内容
        self.text_input.setPlainText('\n'.join(remaining_lines))

        if len(selected_indices) > len(text_lines):
            self.add_log(f"警告: 选中的模拟器数量({len(selected_indices)})大于文本行数({len(text_lines)})")

        self.add_log(f"文本输入完成！成功：{success_count}个，失败：{len(selected_indices)-success_count}个")


    def batch_install_apk(self):
        """批量安装APK"""
        apk_path = self.apk_input.text()
        if not apk_path or not os.path.exists(apk_path):
            self.add_log("错误: 请选择有效的APK文件！")
            return

        selected_indices = [idx for idx, checked in self.checked_items.items() if checked]
        if not selected_indices:
            self.add_log("错误: 请选择至少一个模拟器！")
            return

        success_count = 0
        self.add_log("开始批量安装APK...")
        for index in selected_indices:
            if self.controller.install_apk(int(index), apk_path):
                success_count += 1
                self.add_log(f"模拟器 {index} APK安装成功")
            else:
                self.add_log(f"模拟器 {index} APK安装失败")

        self.add_log(f"APK安装完成！成功：{success_count}个，失败：{len(selected_indices)-success_count}个")

    def batch_uninstall_apk(self):
        """批量卸载APK"""
        package_name = self.package_label.text()
        if not package_name or package_name == "无法获取包名":
            self.add_log("错误: 无法获取APK包名！")
            return

        selected_indices = [idx for idx, checked in self.checked_items.items() if checked]
        if not selected_indices:
            self.add_log("错误: 请选择至少一个模拟器！")
            return

        success_count = 0
        self.add_log("开始批量卸载APK...")
        for index in selected_indices:
            if self.controller.uninstall_apk(int(index), package_name):
                success_count += 1
                self.add_log(f"模拟器 {index} APK卸载成功")
            else:
                self.add_log(f"模拟器 {index} APK卸载失败")

        self.add_log(f"APK卸载完成！成功：{success_count}个，失败：{len(selected_indices)-success_count}个")

    def start_ocr_recognition(self):
        """开始 OCR 识别流程"""
        username = self.ocr_user_input.text()
        password = self.ocr_pass_input.text()

        if not username or not password:
            self.add_log("错误: 请输入打码平台用户名和密码！")
            return

        selected_indices = [idx for idx, checked in self.checked_items.items() if checked]
        if not selected_indices:
            self.add_log("错误: 请选择至少一个模拟器！")
            return
        instances = self.controller.get_all_instances()
        selected_ports = {}
        for instance in instances:
            if instance['index'] in selected_indices:
                selected_ports[instance['index']] = instance['port']

        futures = []
        for index, port in selected_ports.items():
            # 使用 self.process_emulator_ocr 调用
            future = self.executor.submit(self.process_emulator_ocr, index, username, password, port)
            futures.append(future)

        # 检查线程池任务的异常
        for future in as_completed(futures):
            try:
                future.result()  # 获取结果，如果有异常会在这里抛出
            except Exception as e:
                self.add_log(f"线程池任务出错: {type(e).__name__}: {e}")

    def process_emulator_ocr(self, index, username, password, port):
        """处理单个模拟器的 OCR 识别和点击"""

        screenshot_path = self.controller.take_screenshot(index, port)
        if not screenshot_path:
            self.add_log(f"模拟器 {index} 截图失败")
            return

        try:
            # 裁剪图像
            image = Image.open(screenshot_path)
            cropped_image = image.crop((self.CROP_X, self.CROP_Y, self.CROP_X + self.CROP_WIDTH, self.CROP_Y + self.CROP_HEIGHT))

            # 将裁剪后的图像保存到内存中的 BytesIO 对象
            img_byte_arr = BytesIO()
            cropped_image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()  # 获取字节数据

            # 调用 base64_api_bytes 函数 (接受字节数据)
            result = base64_api_bytes(username, password, img_byte_arr)

            if result and result.get("success"):
                data = result.get("data", {})
                if data:
                    result_str = data.get("result")
                    if result_str:
                        coordinates = result_str
                        points = coordinates.split("|")
                        for point in points:
                            try:
                                x, y = [int(coord) for coord in point.split(",")]

                                # ******* 坐标调整 *******
                                adjusted_x = x + self.CROP_X
                                adjusted_y = y + self.CROP_Y

                                if self.controller.tap_screen(index, adjusted_x, adjusted_y, port):  # 使用调整后的坐标
                                    self.add_log(f"模拟器 {index} 点击成功: ({adjusted_x}, {adjusted_y})") # 记录调整后的坐标
                                else:
                                    self.add_log(f"模拟器 {index} 点击失败")
                            except Exception as e:
                                self.add_log(f"模拟器 {index} 解析坐标或点击时出错: {type(e).__name__}: {e}")
                    else:
                        self.add_log(f"模拟器 {index}: OCR 识别结果为空")
                else:
                    self.add_log(f"模拟器 {index}: OCR 识别结果为空或格式错误")
            else:
                error_message = result.get("errstr") if result else "未知错误"
                self.add_log(f"模拟器 {index} 识别失败: {error_message}")

        except Exception as e:
            self.add_log(f"模拟器 {index} OCR 识别出错: {type(e).__name__}: {e}")
        finally:
            if os.path.exists(screenshot_path):
                try:
                    os.remove(screenshot_path)
                except Exception as e:
                    self.add_log(f"模拟器{index}:删除截图文件失败: {type(e).__name__}: {e}")



    def closeEvent(self, event):
        """窗口关闭时的处理"""
        if self.refresh_thread and self.refresh_thread.isRunning():
            self.refresh_thread.stop()
            self.refresh_thread.wait()
         # 显式地关闭线程池
        self.executor.shutdown(wait=True)
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == '__main__':
    main()

